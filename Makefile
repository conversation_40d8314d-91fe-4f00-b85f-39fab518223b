.PHONY: test export-requirements server help clean

# Default target
help:
	@echo "Available targets:"
	@echo "  test               - Run all tests using pytest"
	@echo "  export-requirements - Export dependencies to requirements.txt without hashes"
	@echo "  server             - Run the FastAPI server"
	@echo "  clean              - Clean up cache files"
	@echo "  help               - Show this help message"

# Run tests
test:
	@echo "Running tests..."
	pytest -v

# Export dependencies to requirements.txt without hashes
export-requirements:
	@echo "Exporting dependencies to requirements.txt..."
	uv export --no-hashes --format requirements-txt > requirements.txt
	@echo "Dependencies exported to requirements.txt"

# Run the FastAPI server
server:
	@echo "Starting FastAPI server..."
	python main.py

# Clean up cache files
clean:
	@echo "Cleaning up cache files..."
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name "*.pyc" -delete 2>/dev/null || true
	@echo "Cache files cleaned"