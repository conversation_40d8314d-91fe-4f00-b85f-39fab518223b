#!/usr/bin/env python3
"""
Test script for caching functionality in the face validation service.
"""

import asyncio
import time

import numpy as np
import pytest

from app.helpers import (
    detect_faces_in_image,
    download_image_to_memory,
    get_cache_manager,
)
from app.helpers.image_processor import ImageProcessor
from app.logging import init_logger, logger

# Initialize logging
init_logger()


def test_cache_manager():
    """Test basic cache manager functionality."""
    logger.info("Testing cache manager...")

    cache_manager = get_cache_manager()

    # Test basic put/get
    test_key = "test_key"
    test_data = {"message": "Hello, cache!"}

    # Put data
    success = cache_manager.put(test_key, test_data)
    logger.info(f"Cache put success: {success}")

    # Get data
    retrieved_data = cache_manager.get(test_key)
    logger.info(f"Retrieved data: {retrieved_data}")

    # Check if data matches
    if retrieved_data == test_data:
        logger.info("✓ Cache manager basic functionality works")
    else:
        logger.error("✗ Cache manager basic functionality failed")

    # Test cache stats
    stats = cache_manager.get_stats()
    logger.info(f"Cache stats: {stats}")


@pytest.mark.asyncio
async def test_image_download_caching():
    """Test image download caching."""
    logger.info("Testing image download caching...")

    # Use a small test image URL
    test_url = "https://camo.githubusercontent.com/8389428d9a8ff67ac0dbb923e5c8d7509622f420776c896539c89374d91fb68e/68747470733a2f2f696e7369676874666163652e61692f6173736574732f696d672f637573746f6d2f6c6f676f332e6a7067"

    # First download (should hit network)
    logger.info("First download (should hit network)...")
    start_time = time.time()
    image1, error1 = await download_image_to_memory(test_url)
    first_time = time.time() - start_time

    if error1:
        logger.error(f"First download failed: {error1}")
        return

    logger.info(f"First download took: {first_time:.3f}s")

    # Second download (should hit cache)
    logger.info("Second download (should hit cache)...")
    start_time = time.time()
    image2, error2 = await download_image_to_memory(test_url)
    second_time = time.time() - start_time

    if error2:
        logger.error(f"Second download failed: {error2}")
        return

    logger.info(f"Second download took: {second_time:.3f}s")

    # Compare results
    if np.array_equal(image1, image2):
        logger.info("✓ Images are identical")
    else:
        logger.error("✗ Images are different")

    # Check if second download was faster (indicating cache hit)
    if second_time < first_time * 0.5:  # Should be significantly faster
        logger.info(f"✓ Cache hit detected (speedup: {first_time/second_time:.1f}x)")
    else:
        logger.warning(
            f"? Cache may not have been hit (times: {first_time:.3f}s vs {second_time:.3f}s)"
        )


def test_face_detection_caching():
    """Test face detection caching."""
    logger.info("Testing face detection caching...")

    # Create a test image
    test_image = np.random.randint(0, 255, (300, 300, 3), dtype=np.uint8)

    # First detection (should compute)
    logger.info("First face detection (should compute)...")
    start_time = time.time()
    has_faces1, error1 = detect_faces_in_image(test_image)
    first_time = time.time() - start_time

    if error1:
        logger.error(f"First detection failed: {error1}")
        return

    logger.info(f"First detection took: {first_time:.3f}s, has_faces: {has_faces1}")

    # Second detection (should hit cache)
    logger.info("Second face detection (should hit cache)...")
    start_time = time.time()
    has_faces2, error2 = detect_faces_in_image(test_image)
    second_time = time.time() - start_time

    if error2:
        logger.error(f"Second detection failed: {error2}")
        return

    logger.info(f"Second detection took: {second_time:.3f}s, has_faces: {has_faces2}")

    # Compare results
    if has_faces1 == has_faces2:
        logger.info("✓ Detection results are identical")
    else:
        logger.error("✗ Detection results are different")

    # Check if second detection was faster (indicating cache hit)
    if second_time < first_time * 0.5:  # Should be significantly faster
        logger.info(f"✓ Cache hit detected (speedup: {first_time/second_time:.1f}x)")
    else:
        logger.warning(
            f"? Cache may not have been hit (times: {first_time:.3f}s vs {second_time:.3f}s)"
        )


def test_image_preprocessing_caching():
    """Test image preprocessing caching."""
    logger.info("Testing image preprocessing caching...")

    # Create a test image
    test_image = np.random.randint(0, 255, (800, 600, 3), dtype=np.uint8)

    # First preprocessing (should compute)
    logger.info("First preprocessing (should compute)...")
    start_time = time.time()
    processed1, info1 = ImageProcessor.preprocess_for_detection(test_image)
    first_time = time.time() - start_time

    logger.info(f"First preprocessing took: {first_time:.3f}s")

    # Second preprocessing (should hit cache)
    logger.info("Second preprocessing (should hit cache)...")
    start_time = time.time()
    processed2, info2 = ImageProcessor.preprocess_for_detection(test_image)
    second_time = time.time() - start_time

    logger.info(f"Second preprocessing took: {second_time:.3f}s")

    # Compare results
    if np.array_equal(processed1, processed2):
        logger.info("✓ Processed images are identical")
    else:
        logger.error("✗ Processed images are different")

    # Check if second preprocessing was faster (indicating cache hit)
    if second_time < first_time * 0.5:  # Should be significantly faster
        logger.info(f"✓ Cache hit detected (speedup: {first_time/second_time:.1f}x)")
    else:
        logger.warning(
            f"? Cache may not have been hit (times: {first_time:.3f}s vs {second_time:.3f}s)"
        )


async def main():
    """Run all cache tests."""
    logger.info("Starting cache functionality tests...")

    try:
        test_cache_manager()
        print()

        await test_image_download_caching()
        print()

        test_face_detection_caching()
        print()

        test_image_preprocessing_caching()
        print()

        # Final cache stats
        cache_manager = get_cache_manager()
        final_stats = cache_manager.get_stats()
        logger.info(f"Final cache stats: {final_stats}")

        logger.info("All cache tests completed!")

    except Exception as e:
        logger.error(f"Test failed with error: {str(e)}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
