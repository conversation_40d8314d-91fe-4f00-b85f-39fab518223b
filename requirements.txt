# Production requirements - optimized for Docker deployment
# Excludes development and testing dependencies

# Core web framework
fastapi==0.115.12
uvicorn==0.34.2
uvloop==0.21.0
httptools==0.6.4
starlette==0.46.2

# HTTP client
httpx==0.28.1
httpcore==1.0.9
h11==0.16.0
anyio==4.9.0
sniffio==1.3.1
certifi==2025.4.26
idna==3.10

# Face detection and computer vision
insightface==0.7.3
onnxruntime==1.22.0
opencv-python-headless==*********  # Headless version for containers
numpy==2.2.6
pillow==11.2.1

# Image processing dependencies
albumentations==2.0.8
albucore==0.0.24
scikit-image==0.25.2
scikit-learn==1.6.1
scipy==1.15.3
imageio==2.37.0
tifffile==2025.5.26
networkx==3.5
lazy-loader==0.4

# ONNX and ML dependencies
onnx==1.18.0
protobuf==6.31.1
flatbuffers==25.2.10
sympy==1.14.0
mpmath==1.3.0

# Data validation
pydantic==2.11.5
pydantic-core==2.33.2
annotated-types==0.7.0
typing-extensions==4.13.2
typing-inspection==0.4.1

# Logging
loguru==0.7.3
coloredlogs==15.0.1
humanfriendly==10.0

# Configuration
python-dotenv==1.1.0

# Utilities
click==8.2.1
tqdm==4.67.1
prettytable==3.16.0
easydict==1.13
joblib==1.5.1
threadpoolctl==3.6.0
packaging==25.0
six==1.17.0
wcwidth==0.2.13

# Math and algorithms
simsimd==6.2.1
stringzilla==3.12.5

# Build dependencies (needed for some packages)
cython==3.1.1

# YAML support
pyyaml==6.0.2

# Date utilities
python-dateutil==2.9.0.post0

# URL handling
urllib3==2.4.0
charset-normalizer==3.4.2
