#!/bin/bash

# Docker build script for face validation service
# Provides easy commands for building and testing optimized Docker images

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
IMAGE_NAME="face-validation"
TAG="latest"
TARGET="production"
PUSH=false
REGISTRY=""

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS] COMMAND"
    echo ""
    echo "Commands:"
    echo "  build       Build Docker image"
    echo "  dev         Build development image"
    echo "  test        Build and test image"
    echo "  push        Build and push to registry"
    echo "  clean       Clean up Docker resources"
    echo "  size        Compare image sizes"
    echo "  help        Show this help message"
    echo ""
    echo "Options:"
    echo "  -n, --name NAME     Image name (default: face-validation)"
    echo "  -t, --tag TAG       Image tag (default: latest)"
    echo "  -r, --registry REG  Registry URL for push"
    echo "  --no-cache          Build without cache"
    echo ""
    echo "Examples:"
    echo "  $0 build                          # Build production image"
    echo "  $0 dev                           # Build development image"
    echo "  $0 test                          # Build and test image"
    echo "  $0 -t v1.0.0 build              # Build with specific tag"
    echo "  $0 -r myregistry.com push        # Build and push to registry"
    echo "  $0 --no-cache build              # Build without cache"
}

# Parse command line arguments
CACHE_FLAG=""
while [[ $# -gt 0 ]]; do
    case $1 in
        -n|--name)
            IMAGE_NAME="$2"
            shift 2
            ;;
        -t|--tag)
            TAG="$2"
            shift 2
            ;;
        -r|--registry)
            REGISTRY="$2"
            shift 2
            ;;
        --no-cache)
            CACHE_FLAG="--no-cache"
            shift
            ;;
        build|dev|test|push|clean|size|help)
            COMMAND="$1"
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Set full image name
if [[ -n "$REGISTRY" ]]; then
    FULL_IMAGE_NAME="$REGISTRY/$IMAGE_NAME:$TAG"
else
    FULL_IMAGE_NAME="$IMAGE_NAME:$TAG"
fi

# Function to build production image
build_production() {
    print_status "Building production image: $FULL_IMAGE_NAME"
    
    docker build \
        --target production \
        --tag "$FULL_IMAGE_NAME" \
        $CACHE_FLAG \
        .
    
    print_success "Production image built successfully"
    
    # Show image size
    SIZE=$(docker images --format "table {{.Size}}" "$FULL_IMAGE_NAME" | tail -n 1)
    print_status "Image size: $SIZE"
}

# Function to build development image
build_development() {
    DEV_IMAGE_NAME="$IMAGE_NAME:dev"
    print_status "Building development image: $DEV_IMAGE_NAME"
    
    docker build \
        --target builder \
        --tag "$DEV_IMAGE_NAME" \
        $CACHE_FLAG \
        .
    
    print_success "Development image built successfully"
}

# Function to test image
test_image() {
    print_status "Testing image: $FULL_IMAGE_NAME"
    
    # Build if not exists
    if ! docker images --format "{{.Repository}}:{{.Tag}}" | grep -q "^$FULL_IMAGE_NAME$"; then
        print_warning "Image not found, building first..."
        build_production
    fi
    
    # Test container startup
    print_status "Testing container startup..."
    CONTAINER_ID=$(docker run -d -p 8000:8000 "$FULL_IMAGE_NAME")
    
    # Wait for startup
    print_status "Waiting for service to start..."
    sleep 10
    
    # Test health endpoint
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        print_success "Health check passed"
    else
        print_error "Health check failed"
        docker logs "$CONTAINER_ID"
        docker stop "$CONTAINER_ID" > /dev/null
        docker rm "$CONTAINER_ID" > /dev/null
        exit 1
    fi
    
    # Test API endpoint
    if curl -f "http://localhost:8000/config" > /dev/null 2>&1; then
        print_success "API endpoint test passed"
    else
        print_warning "API endpoint test failed"
    fi
    
    # Cleanup
    docker stop "$CONTAINER_ID" > /dev/null
    docker rm "$CONTAINER_ID" > /dev/null
    
    print_success "Image testing completed successfully"
}

# Function to push image
push_image() {
    if [[ -z "$REGISTRY" ]]; then
        print_error "Registry not specified. Use -r option to specify registry."
        exit 1
    fi
    
    print_status "Building and pushing image: $FULL_IMAGE_NAME"
    
    # Build image
    build_production
    
    # Push image
    print_status "Pushing image to registry..."
    docker push "$FULL_IMAGE_NAME"
    
    print_success "Image pushed successfully"
}

# Function to clean up Docker resources
clean_docker() {
    print_status "Cleaning up Docker resources..."
    
    # Remove dangling images
    if docker images -f "dangling=true" -q | grep -q .; then
        print_status "Removing dangling images..."
        docker rmi $(docker images -f "dangling=true" -q)
    fi
    
    # Remove unused containers
    if docker ps -a -f "status=exited" -q | grep -q .; then
        print_status "Removing stopped containers..."
        docker rm $(docker ps -a -f "status=exited" -q)
    fi
    
    # Prune build cache
    print_status "Pruning build cache..."
    docker builder prune -f
    
    print_success "Docker cleanup completed"
}

# Function to compare image sizes
compare_sizes() {
    print_status "Comparing image sizes..."
    
    echo ""
    echo "Current images:"
    docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}" | grep "$IMAGE_NAME" || true
    
    echo ""
    print_status "Building both production and development images for comparison..."
    
    # Build both images
    build_production
    build_development
    
    echo ""
    echo "Size comparison:"
    docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" | grep "$IMAGE_NAME"
}

# Main command execution
case "${COMMAND:-help}" in
    build)
        build_production
        ;;
    dev)
        build_development
        ;;
    test)
        test_image
        ;;
    push)
        push_image
        ;;
    clean)
        clean_docker
        ;;
    size)
        compare_sizes
        ;;
    help)
        show_usage
        ;;
    *)
        print_error "Unknown command: ${COMMAND:-}"
        show_usage
        exit 1
        ;;
esac
