import threading
import time
from typing import List, Optional, Tuple

import insightface
import numpy as np

from app.config import CACHE_FACE_DETECTION, MODEL_DETECTION_SIZE, MODEL_WARMUP_ENABLED
from app.logging import logger


class FaceDetector:
    """Face detector using InsightFace for CPU-only detection with optimized loading."""

    def __init__(self, preload: bool = True):
        """Initialize the face detector with InsightFace model.

        Args:
            preload: Whether to immediately load the model or defer until first use
        """
        self.app: Optional[insightface.app.FaceAnalysis] = None
        self._initialization_lock = threading.Lock()
        self._is_initializing = False
        self._initialization_error: Optional[str] = None
        self._load_start_time: Optional[float] = None

        if preload:
            self._initialize_model()

    def _initialize_model(self) -> bool:
        """Initialize the InsightFace model with thread safety.

        Returns:
            True if initialization successful, False otherwise
        """
        if self.app is not None:
            return True

        with self._initialization_lock:
            # Double-check pattern
            if self.app is not None:
                return True

            if self._is_initializing:
                # Another thread is initializing, wait for it
                while self._is_initializing and self.app is None:
                    time.sleep(0.1)
                return self.app is not None

            self._is_initializing = True
            self._load_start_time = time.time()

            try:
                logger.info("Starting InsightFace model initialization...")

                # Initialize InsightFace app with CPU providers only
                self.app = insightface.app.FaceAnalysis(
                    providers=["CPUExecutionProvider"],  # Force CPU-only processing
                    allowed_modules=[
                        "detection"
                    ],  # Only load detection module for efficiency
                )

                # Prepare the model (downloads models if needed)
                det_size = (MODEL_DETECTION_SIZE, MODEL_DETECTION_SIZE)
                self.app.prepare(ctx_id=0, det_size=det_size)

                load_time = time.time() - self._load_start_time
                logger.info(
                    f"InsightFace detector initialized successfully with CPU providers "
                    f"(load time: {load_time:.2f}s, detection size: {det_size})"
                )

                # Perform a quick warmup with a small test image
                if MODEL_WARMUP_ENABLED:
                    self._warmup_model()

                return True

            except Exception as e:
                error_msg = f"Failed to initialize InsightFace detector: {str(e)}"
                logger.error(error_msg)
                self._initialization_error = error_msg
                self.app = None
                return False
            finally:
                self._is_initializing = False

    def _warmup_model(self) -> None:
        """Warm up the model with a small test image to ensure it's ready."""
        try:
            if self.app is None:
                logger.warning("Cannot warm up model: app is None")
                return

            # Create a small test image (64x64 RGB)
            test_image = np.random.randint(0, 255, (64, 64, 3), dtype=np.uint8)

            # Use optimized preprocessing for warmup
            from app.helpers.image_processor import ImageProcessor

            test_image_bgr = ImageProcessor.rgb_to_bgr_optimized(test_image)

            # Run a quick detection to warm up the model
            _ = self.app.get(test_image_bgr)
            logger.info("Model warmup completed successfully")

        except Exception as e:
            logger.warning(f"Model warmup failed (non-critical): {str(e)}")

    def is_ready(self) -> bool:
        """Check if the face detector is ready for use.

        Returns:
            True if model is loaded and ready, False otherwise
        """
        return self.app is not None and not self._is_initializing

    def get_initialization_status(self) -> dict:
        """Get detailed initialization status information.

        Returns:
            Dictionary with initialization status details
        """
        return {
            "is_ready": self.is_ready(),
            "is_initializing": self._is_initializing,
            "initialization_error": self._initialization_error,
            "load_time": (
                time.time() - self._load_start_time
                if self._load_start_time and self._is_initializing
                else None
            ),
        }

    def detect_faces(
        self, image: np.ndarray
    ) -> Tuple[bool, List[Tuple[int, int, int, int]], str | None]:
        """
        Detect faces in an image using InsightFace.

        Args:
            image: Input image as numpy array (RGB format)

        Returns:
            Tuple of (has_faces, face_rectangles, error_message)
            face_rectangles is a list of (x, y, width, height) tuples
        """
        try:
            # Ensure model is initialized
            if not self._initialize_model():
                error_msg = f"Face detector not ready: {self._initialization_error or 'Unknown error'}"
                return False, [], error_msg

            if image is None or image.size == 0:
                return False, [], "Invalid or empty image"

            # Validate image format and data type
            if len(image.shape) != 3 or image.shape[2] != 3:
                return False, [], "Image must be in RGB format with 3 channels"

            if image.dtype != np.uint8:
                return False, [], f"Image must be uint8 format, got {image.dtype}"

            # Check for reasonable image dimensions
            height, width = image.shape[:2]
            if height < 32 or width < 32:
                return (
                    False,
                    [],
                    f"Image too small for face detection: {width}x{height}",
                )
            if height > 10000 or width > 10000:
                return False, [], f"Image too large: {width}x{height}"

            # Optimize image for face detection (resize + color conversion)
            from app.helpers.image_processor import ImageProcessor

            start_time = time.time()
            optimized_bgr, _ = ImageProcessor.preprocess_for_detection(image)
            preprocessing_time = time.time() - start_time

            # Only log preprocessing details if it took significant time
            if preprocessing_time > 0.05:  # Log if preprocessing took more than 50ms
                logger.debug(f"Image preprocessing: {preprocessing_time:.3f}s")

            # Detect faces using InsightFace
            assert (
                self.app is not None
            ), "Face detector app should be initialized at this point"
            faces = self.app.get(optimized_bgr)

            # Convert InsightFace face objects to bounding box tuples
            face_rectangles = []
            for face in faces:
                # InsightFace returns bbox as [x1, y1, x2, y2]
                bbox = face.bbox.astype(int)
                x, y, x2, y2 = bbox
                w = x2 - x
                h = y2 - y
                face_rectangles.append((x, y, w, h))

            has_faces = len(face_rectangles) > 0

            # Log face detection results concisely
            logger.info(
                f"Face detection: {'✓' if has_faces else '✗'} ({len(face_rectangles)} faces)"
            )

            return has_faces, face_rectangles, None

        except Exception as e:
            error_msg = f"Face detection error: {str(e)}"
            logger.error(error_msg)
            return False, [], error_msg


# Global face detector instance to avoid reloading model on every request
_global_face_detector: Optional[FaceDetector] = None
_global_detector_lock = threading.Lock()


def _get_face_detector(preload: bool = True) -> FaceDetector:
    """Get or create the global face detector instance with thread safety.

    Args:
        preload: Whether to preload the model immediately

    Returns:
        FaceDetector instance
    """
    global _global_face_detector

    # Check without lock first (fast path)
    if _global_face_detector is not None:
        return _global_face_detector

    # Acquire lock for creation (slow path)
    with _global_detector_lock:
        # Double-check pattern - check again inside lock
        if _global_face_detector is None:
            logger.info("Creating global face detector instance")
            _global_face_detector = FaceDetector(preload=preload)
        return _global_face_detector


def preload_face_detector() -> bool:
    """Preload the face detection model to avoid cold start delays.

    Returns:
        True if preloading successful, False otherwise
    """
    try:
        detector = _get_face_detector(preload=True)
        return detector.is_ready()
    except Exception as e:
        logger.error(f"Failed to preload face detector: {str(e)}")
        return False


def get_face_detector_status() -> dict:
    """Get the current status of the face detector.

    Returns:
        Dictionary with detector status information
    """
    global _global_face_detector

    if _global_face_detector is None:
        return {
            "detector_created": False,
            "is_ready": False,
            "is_initializing": False,
            "initialization_error": None,
            "load_time": None,
        }

    status = _global_face_detector.get_initialization_status()
    status["detector_created"] = True
    return status


def detect_faces_in_image(image: np.ndarray) -> Tuple[bool, str | None]:
    """
    Convenience function to detect if an image contains faces.

    Args:
        image: Input image as numpy array (RGB format)

    Returns:
        Tuple of (has_faces, error_message)
    """
    # Import here to avoid circular imports
    import hashlib

    from .cache_manager import get_cache_manager

    try:
        # Initialize cache variables
        cache_manager = None
        cache_key = None
        image_hash = None

        # Set up caching if enabled
        if CACHE_FACE_DETECTION:
            cache_manager = get_cache_manager()
            image_hash = hashlib.sha256(image.tobytes()).hexdigest()
            cache_key = cache_manager.generate_key(
                image_hash, image.shape, prefix="face_detection"
            )

            # Check cache first
            cached_result = cache_manager.get(cache_key)
            if cached_result is not None:
                logger.debug("Face detection result loaded from cache")
                return cached_result

        # Perform face detection
        detector = _get_face_detector()
        has_faces, _, error = detector.detect_faces(image)
        result = (has_faces, error)

        # Cache the result if caching is enabled and no error occurred
        if (
            CACHE_FACE_DETECTION
            and error is None
            and cache_manager is not None
            and cache_key is not None
        ):
            cache_manager.put(
                cache_key,
                result,
                metadata={
                    "image_shape": image.shape,
                    "has_faces": has_faces,
                    "image_hash": image_hash[:16] if image_hash else "unknown",
                },
            )
            logger.debug(f"Cached face detection result: {has_faces}")

        return result

    except Exception as e:
        error_msg = f"Failed to initialize face detector: {str(e)}"
        logger.error(error_msg)
        return False, error_msg
