# 1.0.0 (2025-05-30)


### Bug Fixes

* change service name and description to fit it's usecase ([7c539c3](https://dev.azure.com/predictintel/data-ai/_git/face-validation/commit/7c539c3461f8cf0fda73710f9f27a5c5767b430b))
* Dockerfile and requirements ([3416f23](https://dev.azure.com/predictintel/data-ai/_git/face-validation/commit/3416f236a9998abfb2856eeea6282c0fff1268c7))
* Dockerfile optimizations ([d28d1c6](https://dev.azure.com/predictintel/data-ai/_git/face-validation/commit/d28d1c6b06be5f3293f284a7092b833b33bdfb70))
* don't load model for every request ([db8d0b5](https://dev.azure.com/predictintel/data-ai/_git/face-validation/commit/db8d0b52256148b382f25e51beeb28e745f6515c))
* remove logs dir from repo ([55b70cc](https://dev.azure.com/predictintel/data-ai/_git/face-validation/commit/55b70cc9d2bc5800ab1a5f5e269a961fdec5b2c5))
* resolve memory corruption and race conditions in image processing ([06c09bf](https://dev.azure.com/predictintel/data-ai/_git/face-validation/commit/06c09bf6f602d1d937aee8d8e589aa02d56cbcc1))
* Typo ([9c13e5d](https://dev.azure.com/predictintel/data-ai/_git/face-validation/commit/9c13e5d359ef4817901696d1b4ccec04b0233128))


### Features

* async mage downloads, timeouts and connection pooling ([eff148f](https://dev.azure.com/predictintel/data-ai/_git/face-validation/commit/eff148f615e4c72bf84b5853ac65494ec521cdde))
* **cache:** implement comprehensive caching system for network requests and processing operations ([6831e50](https://dev.azure.com/predictintel/data-ai/_git/face-validation/commit/6831e50e38b5bb92925c295ce7f9884f5ccfe902))
* implement faster image processing (configurable) ([9a44431](https://dev.azure.com/predictintel/data-ai/_git/face-validation/commit/9a44431091c2c4c7e7291357bbaed6e0ba2a3496))
* include ci/cd files ([b9c8ce4](https://dev.azure.com/predictintel/data-ai/_git/face-validation/commit/b9c8ce4b479b0655b4d6cc8f3b27f7e3be0811d2))
* include logs file in gitignore ([4d87b02](https://dev.azure.com/predictintel/data-ai/_git/face-validation/commit/4d87b0220e4eef33794b41378b8d689a358dd830))
* initial commit ([e6aa736](https://dev.azure.com/predictintel/data-ai/_git/face-validation/commit/e6aa73694ab2b0ca274e67e48050c42dad68f97c))
* optimize face detection model loading with thread-safe preloading and health monitoring ([f07d5a8](https://dev.azure.com/predictintel/data-ai/_git/face-validation/commit/f07d5a8441ae13f88222e0bcbb887e928a71a9ca))
* optimize image processing with intelligent resizing, efficient color conversion ([4d43628](https://dev.azure.com/predictintel/data-ai/_git/face-validation/commit/4d4362828b15d73aae2dd66bd25edb91fe9486e2))
