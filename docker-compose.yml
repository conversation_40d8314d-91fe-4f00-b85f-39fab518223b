version: '3.8'

services:
  face-validation:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    ports:
      - "8000:8000"
    environment:
      # Service configuration
      - ENVIRONMENT=production
      - LOG_LEVEL=INFO
      - HOST=0.0.0.0
      - PORT=8000
      
      # Model configuration
      - PRELOAD_MODEL=true
      - M<PERSON><PERSON>_WARMUP_ENABLED=true
      - MODEL_DETECTION_SIZE=640
      
      # Image processing optimization
      - ULTRA_FAST_PROCESSING=true
      - MAX_IMAGE_DIMENSION=1024
      - IMAGE_RESIZE_ENABLED=true
      - IMAGE_QUALITY_OPTIMIZATION=false  # Disable for speed in production
      - PRESERVE_ASPECT_RATIO=true
      
      # Caching configuration
      - CACHE_ENABLED=true
      - CACHE_TYPE=memory
      - CACHE_MAX_SIZE_MB=500
      - CACHE_TTL_SECONDS=3600
      - CACHE_IMAGE_DOWNLOADS=true
      - CACHE_FACE_DETECTION=true
      - CACHE_IMAGE_PROCESSING=true
      
      # HTTP configuration
      - HTTP_TIMEOUT_SECONDS=30
      - MAX_CONCURRENT_DOWNLOADS=10
      
    volumes:
      # Optional: Mount cache directory for persistence
      - ./cache:/app/cache
      - ./logs:/app/logs
    
    healthcheck:
      test: ["CMD", "python", "-c", "import httpx; httpx.get('http://localhost:8000/health', timeout=5)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    restart: unless-stopped
    
    # Resource limits (adjust based on your needs)
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'

  # Development service with hot reload
  face-validation-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: builder
    ports:
      - "8001:8000"
    environment:
      - ENVIRONMENT=development
      - LOG_LEVEL=DEBUG
      - PRELOAD_MODEL=false  # Faster startup in dev
      - ULTRA_FAST_PROCESSING=true
      - CACHE_ENABLED=false  # Disable caching in dev for testing
    volumes:
      - .:/app
      - ./cache:/app/cache
      - ./logs:/app/logs
    command: ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
    profiles:
      - dev

volumes:
  cache:
  logs:
