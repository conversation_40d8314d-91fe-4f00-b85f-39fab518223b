# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore
.gitattributes

# Documentation
*.md
docs/
README*
CHANGELOG*
LICENSE*

# Testing
.pytest_cache/
.coverage
.nyc_output
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Logs
logs/
*.log

# Development files
.env.example
.env.local
.env.development
.env.test
docker-compose*.yml
Makefile

# CI/CD
.github/
.gitlab-ci.yml
azure-pipelines.yml
.circleci/
.travis.yml

# Package managers
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Temporary files
tmp/
temp/
.tmp/

# Editor backups
*~
*.bak
*.tmp
*.orig

# UV lock file (not needed in container)
uv.lock

# Test files
test_*.py
*_test.py
tests/

# Development scripts
scripts/
tools/

# Local configuration
local.env
.local

# Jupyter notebooks
*.ipynb
.ipynb_checkpoints/

# PyCharm
.idea/

# Optimization documentation (not needed in runtime)
*OPTIMIZATIONS*.md
CACHING.md
